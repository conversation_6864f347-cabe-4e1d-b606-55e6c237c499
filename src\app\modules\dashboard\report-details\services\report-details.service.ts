import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiHandler } from 'src/app/shared-services/ep-api-handler/ep-api-handler';
import { ApiTypes, ApiRoutes } from 'src/app/shared-services/ep-api-handler/api-option-enums';
import { UserContext } from 'src/app/shared-services/user-context/user-context.service';
import { ReportDetail } from 'src/app/shared-services/bonus-measures/model/report-details-model';

export interface ReportDetailsParams {
  siteId: string;
  year: number;
  cohortId: number;
  locationCd: string;
  providerCd: string;
  rollingWeek: number;
  alertLvl: boolean;
  measuresCd: string;
}

@Injectable({
  providedIn: 'root'
})
export class ReportDetailsService {

  constructor(
    private apiHandler: ApiHandler,
    private userContext: UserContext
  ) { }

  /**
   * Get report details data
   * @param params Parameters for the API call
   * @returns Observable with report detail data
   */
  public getReportDetails(params: ReportDetailsParams): Observable<ReportDetail[]> {
    // Build the API route with parameters
    const apiRoute = ApiRoutes.QualityMeasuresDetail
      .replace('{{siteId}}', params.siteId)
      .replace('{{year}}', params.year.toString())
      .replace('{{cohortId}}', params.cohortId.toString())
      .replace('{{locationCd}}', encodeURIComponent(params.locationCd))
      .replace('{{providerCd}}', encodeURIComponent(params.providerCd))
      .replace('{{rollingWeek}}', params.rollingWeek.toString())
      .replace('{{alertLvl}}', params.alertLvl.toString())
      .replace('{{measuresCd}}', params.measuresCd);
    
    // Make the API call and transform the data
    return this.apiHandler.Get<any[]>(
      ApiTypes.V2,
      apiRoute,
      false
    ).pipe(
      map(data => this.transformApiResponse(data))
    );
  }

  /**
   * Transform the API response to match our model and add calculated fields
   * @param apiData Raw API response data
   * @returns Transformed report details data
   */
  private transformApiResponse(apiData: any[]): ReportDetail[] {
    return apiData.map(item => ({
      cohortId: item.cohortId,
      patientId: item.patientId,
      mrn: item.mrn,
      patientName: item.patientName,
      birthDt: item.birthDt,
      lastVisitDt: item.lastVisitDt,
      alertLvl: item.alertLvl,
      providerId: item.providerId,
      actionTxt: item.actionTxt,
      detailTxt: item.detailTxt,
      measureId: item.measureId,
      invertedFlg: item.invertedFlg,
      measureAnnotateId: item.measureAnnotateId,
      openFlg: item.openFlg,
      measureDt: item.measureDt,
      intervalDesc: item.intervalDesc,
      
      // Calculate derived fields
      age: this.calculateAge(item.birthDt),
      formattedBirthDate: this.formatDate(item.birthDt),
      formattedLastVisitDate: this.formatDate(item.lastVisitDt),
      formattedMeasureDate: this.formatDate(item.measureDt),
      annotate: "Annotate"
    }));
  }

  /**
   * Calculate age from birth date
   * @param birthDate Birth date string
   * @returns Age in years
   */
  private calculateAge(birthDate: string): number {
    if (!birthDate) return 0;
    
    const birth = new Date(birthDate);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  }

  /**
   * Format date for display
   * @param dateString Date string to format
   * @returns Formatted date string in YYYY-MM-DD format
   */
  private formatDate(dateString: string): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }
}
