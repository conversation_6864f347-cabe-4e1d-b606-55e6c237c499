/* Report Details Component Styles */
.report-details-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: MuseoSans-300;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header */
.header {
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.patient-count {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

/* Export Dropdown Styles */
.export-dropdown {
  position: relative;
}

.downloadButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #0071BC;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.downloadButton:hover {
  background-color: #005a94;
}

.download-icon {
  font-size: 18px;
}

.dropdown {
  position: relative;
}

.menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 200px;
}

.menu ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.menu li {
  border-bottom: 1px solid #eee;
}

.menu li:last-child {
  border-bottom: none;
}

.menu a {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  text-decoration: none;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu a:hover {
  background-color: #f5f5f5;
}

.menu-header {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ddd;
}

.menu-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #0071BC;
}

.menu-icon {
  font-size: 18px;
}

/* Loading State */
.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-content {
  text-align: center;
}

/* No Data State */
.no-data-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.no-data-content {
  text-align: center;
  color: #6c757d;
}

.no-data-icon {
  font-size: 48px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.no-data-content h3 {
  margin: 16px 0 8px 0;
  color: #495057;
}

.no-data-content p {
  margin: 0;
  font-size: 14px;
}

/* Table Container */
.table-container {
  flex: 1;
  overflow: hidden;
  padding: 16px;
}

#report-details-table {
  height: 100%;
}
